import React, { useEffect, useRef, useState } from 'react';
import { gsap } from 'gsap';
import AstronomicalLayer from './AstronomicalLayer';
import DiurnalLayer from './DiurnalLayer';
// 🎬 CISCO: SUPPRESSION useDayCycleOptional - Remplacé par props directes du système Cinema
// 🎨 CISCO: IMPORT DES VRAIES PALETTES - Remplacement des couleurs pastels (SUPPRIMÉ - non utilisé)

// 🔧 CISCO: Système de rotation supprimé - Background fixe pour éviter les changements automatiques

// 🔧 CISCO: Fonction supprimée - Background fixe pour éviter les changements automatiques

// 🎬 CISCO: SYSTÈME CINÉMATOGRAPHIQUE SIMPLIFIÉ - 4 MODES UNIQUEMENT
// Types pour les modes de fond cinématographiques (correspondance avec CinemaPhase)
type BackgroundMode =
  | 'dawn'        // Aube (correspond à 'aube')
  | 'midday'      // Midi (correspond à 'midi')
  | 'sunset'      // Coucher (correspond à 'coucher')
  | 'night';      // Nuit (correspond à 'nuit')

// 🔧 CISCO: MODES SUPPRIMÉS (simplification) : sunrise, morning, afternoon, dusk

// 🎨 CISCO: VRAIES PALETTES CISCO - Remplacement complet des couleurs pastels
// ✨ NOUVELLES COULEURS: Extraites des vraies captures d'écran de ciels naturels
// Dégradés fluides synchronisés avec le temporisateur de journée

// 🎬 CISCO: PALETTES CINÉMATOGRAPHIQUES SIMPLIFIÉES - 4 MODES ESSENTIELS
// Dégradés cohérents selon les règles d'or CISCO (nuages/paysage/arrière-plan)
const BACKGROUND_MODES = {
  // 🌅 === AUBE === 🌅
  // CISCO: Dégradé manuel selon spécifications - Bleu sombre → Bleu clair → Rose pastel
  dawn: {
    primary: '#ffb3ba',    // Bas : Rose pastel doux (horizon aube)
    secondary: '#41849eff',  // Milieu : Bleu ciel légèrement plus clair
    tertiary: '#355f96ff'    // Haut : Bleu un peu plus sombre (pas grand jour)
  },

  // ☀️ === MIDI === ☀️
  // CISCO: Lumière au maximum - Bleu ciel éclatant uniforme
  midday: {
    primary: '#e3f2fd',    // Bas : Bleu très clair (horizon lumineux)
    secondary: '#90caf9',  // Milieu : Bleu clair éclatant
    tertiary: '#42a5f5'    // Haut : Bleu pur intense (zénith parfait)
  },

  // 🌇 === COUCHER === 🌇
  // CISCO: Dégradé doux et réaliste - Continuité depuis midi avec couleurs pastel
  sunset: {
    primary: '#ffb3b3',    // Bas : Rouge très clair et doux (horizon pastel)
    secondary: '#ffd4a3',  // Milieu : Orange pastel (milieu écran)
    tertiary: '#87ceeb'    // Haut : Bleu ciel (continuité depuis midi)
  },

  // 🌌 === NUIT === 🌌
  // CISCO: Lumière au minimum - Bleu nuit profond uniforme
  night: {
    primary: '#0d1117',    // Bas : Bleu nuit très sombre (horizon nocturne)
    secondary: '#1c2938',  // Milieu : Bleu nuit intermédiaire
    tertiary: '#2d3748'    // Haut : Bleu nuit plus clair (étoiles visibles)
  }
};

// 🔧 CISCO: TRANSITIONS AVEC VRAIES COULEURS - Ponts naturels entre les modes
// Ces transitions permettent de passer en douceur d'un mode à un autre en utilisant des couleurs intermédiaires.
// Les couleurs sont définies pour trois zones de l'écran :
// - `primary` : correspond à la partie basse de l'écran (proche de l'horizon, le plus clair).
// - `secondary` : correspond à la partie intermédiaire de l'écran (milieu du ciel, intermédiaire).
// - `tertiary` : correspond à la partie haute de l'écran (proche du zénith, le plus sombre).
// Les pourcentages définissent la répartition verticale des couleurs sur l'écran.

// 🔧 CISCO: TRANSITIONS AVEC VRAIES COULEURS - Ponts naturels entre les modes
// Ces transitions permettent de passer en douceur d'un mode à un autre en utilisant des couleurs intermédiaires.
// Les couleurs sont définies pour trois zones de l'écran :
// - `primary` : correspond à la partie basse de l'écran (proche de l'horizon, le plus clair).
// - `secondary` : correspond à la partie intermédiaire de l'écran (milieu du ciel, intermédiaire).
// - `tertiary` : correspond à la partie haute de l'écran (proche du zénith, le plus sombre).
// Les pourcentages définissent la répartition verticale des couleurs sur l'écran.

// 🔧 CISCO: TRANSITIONS SUPPRIMÉES - Utilisation directe des fonctions spécialisées
// Les transitions avec pont intermédiaire causaient le sursaut orange récalcitrant
// Chaque mode utilise maintenant ses propres couleurs directement

// Interface pour les props du composant
interface DynamicBackgroundProps {
  children: React.ReactNode;
  skyMode?: string; // 🔧 CISCO: Optionnel si contexte disponible
}



const DynamicBackground: React.FC<DynamicBackgroundProps> = ({ children, skyMode }) => {
  const [isTransitioning, setIsTransitioning] = useState(false);

  // 🎬 CISCO: SYSTÈME CINÉMATOGRAPHIQUE SIMPLIFIÉ - Props directes uniquement
  const defaultMode = 'dawn';
  const validatedSkyMode = skyMode && typeof skyMode === 'string' ? skyMode : defaultMode;
  const currentModeRef = useRef(validatedSkyMode);
  const backgroundRef = useRef<HTMLDivElement>(null);
  const gradientRef = useRef<HTMLDivElement>(null);
  const landscapeRef = useRef<HTMLDivElement>(null);
  const timelineRef = useRef<gsap.core.Timeline | null>(null);
  const zoomTimelineRef = useRef<gsap.core.Timeline | null>(null);
  // 🔧 CISCO: sunriseAnimationRef supprimé - géré dans AstronomicalLayer

  // 🔧 CISCO: Background UNIQUE - Background.png seulement (simplification)
  const selectedBackground = '/Background.png'; // Background unique pour simplifier

  // 🔧 CISCO: Position simplifiée pour Background.png unique
  const getBackgroundPosition = (): string => {
    return 'center bottom -200px'; // 🔧 CISCO: Paysage complètement en bas pour MAXIMUM de ciel dégagé et lune visible rapidement
  };
  
  // 🔧 CISCO: SUPPRESSION COMPLÈTE - Plus de fonction automatique basée sur l'heure
  // const getModeForTime = ... // SUPPRIMÉ - Mode manuel uniquement



  // 🔧 CISCO: ANCIEN SYSTÈME SUPPRIMÉ - DiurnalLayer s'occupe maintenant de tout

  // 🔧 CISCO: FONCTION SUPPRIMÉE - applyStarsTransition obsolète
  // Les étoiles sont gérées automatiquement par AstronomicalLayer via props skyMode



  // 🎬 CISCO: LOGIQUE CINÉMATOGRAPHIQUE - DIRECTIONS DÉGRADÉS SELON CYCLE SOLAIRE
  // 🌅 PHASES MONTANTES (Soleil monte) : NUIT → AUBE → dégradés `to top`
  // 🌇 PHASES DESCENDANTES (Soleil descend) : MIDI → COUCHER → dégradés `to bottom`

  const getGradientDirection = (mode: BackgroundMode): string => {
    // 🌅 PHASES MONTANTES - Lumière arrive par l'horizon, monte vers le ciel
    if (['night', 'dawn'].includes(mode)) {
      return 'to top';
    }
    // 🌇 PHASES DESCENDANTES - Lumière part du zénith, descend vers l'horizon
    if (['midday', 'sunset'].includes(mode)) {
      return 'to bottom';
    }
    return 'to top'; // Fallback
  };

  // 🔧 CISCO: FONCTIONS SPÉCIALISÉES avec directions correctes selon logique solaire
  const applyNightMode = () => {
    console.log('🌌 APPLICATION MODE NUIT PROFONDE - Dégradé MONTANT (to top)');
    const colors = BACKGROUND_MODES.night;
    const direction = getGradientDirection('night');
    // 🎨 CISCO: Dégradé qui MONTE - Bleu nuit profond uniforme
    const gradient = `linear-gradient(${direction}, ${colors.primary} 0%, ${colors.secondary} 60%, ${colors.tertiary} 100%)`;
    const brightness = 0.15;

    if (gradientRef.current) {
      // 🔧 CISCO: NETTOYAGE EXPLICITE - Arrêter toute animation sur cet élément
      gsap.killTweensOf(gradientRef.current);

      // 🔧 CISCO: ANIMATION DOUCE avec nettoyage préalable
      gsap.to(gradientRef.current, {
        backgroundImage: gradient,
        duration: 15.0,
        ease: "power2.inOut",
        force3D: true,
        onStart: () => {
          console.log(`🎨 NUIT: Démarrage transition vers - ${gradient}`);
        },
        onComplete: () => {
          console.log(`🎨 NUIT: Transition terminée - Dégradé nuit appliqué`);
        }
      });
    }

    if (landscapeRef.current) {
      gsap.to(landscapeRef.current, {
        filter: `brightness(${brightness})`,
        duration: 15.0,
        ease: "power2.inOut"
      });
    }
  };

  const applyDawnMode = () => {
    console.log('🌅 APPLICATION MODE AUBE - Dégradé manuel CISCO - Bleu sombre → Bleu clair → Rose pastel');
    const colors = BACKGROUND_MODES.dawn;
    const direction = getGradientDirection('dawn');
    // 🎨 CISCO: Dégradé manuel spécifique - Rose pastel en bas, bleu clair au milieu, bleu sombre en haut
    const gradient = `linear-gradient(${direction}, ${colors.primary} 0%, ${colors.secondary} 50%, ${colors.tertiary} 100%)`;
    const brightness = 0.5; // 🔧 CISCO: Légèrement plus lumineux pour l'aube

    if (gradientRef.current) {
      // 🔧 CISCO: NETTOYAGE EXPLICITE - Arrêter toute animation sur cet élément
      gsap.killTweensOf(gradientRef.current);

      // 🔧 CISCO: ANIMATION DOUCE avec nettoyage préalable
      gsap.to(gradientRef.current, {
        backgroundImage: gradient,
        duration: 15.0,
        ease: "power2.inOut",
        force3D: true,
        onStart: () => {
          console.log(`🎨 AUBE: Démarrage transition vers - ${gradient}`);
        },
        onComplete: () => {
          console.log(`🎨 AUBE: Transition terminée - Dégradé rose-bleu appliqué`);
        }
      });
    }

    if (landscapeRef.current) {
      gsap.to(landscapeRef.current, {
        filter: `brightness(${brightness})`,
        duration: 15.0,
        ease: "power2.inOut"
      });
    }
  };

  // 🎬 CISCO: applyMorningMode SUPPRIMÉ - Simplifié vers 4 modes cinématographiques

  // 🔧 CISCO: FONCTION SUPPRIMÉE - resetAllLayers obsolète
  // Utilisation directe des fonctions spécialisées dans setBackgroundMode

  // 🎬 CISCO: applySunriseMode SUPPRIMÉ - Simplifié vers 4 modes cinématographiques

  // 🔧 CISCO: NOUVELLES FONCTIONS POUR PHASES DESCENDANTES
  const applyMiddayMode = () => {
    console.log('🌞 APPLICATION MODE MIDI - Continuité douce depuis aube - Bleu azur pur → Bleu pastel');
    const colors = BACKGROUND_MODES.midday;
    const direction = getGradientDirection('midday');
    // 🎨 CISCO: Continuité depuis aube - Bleu azur pur en haut, bleu pastel clair en bas
    const gradient = `linear-gradient(${direction}, ${colors.tertiary} 0%, ${colors.secondary} 40%, ${colors.primary} 100%)`;
    const brightness = 1.0; // 🔧 CISCO: Luminosité maximum à midi

    if (gradientRef.current) {
      // 🔧 CISCO: NETTOYAGE EXPLICITE - Arrêter toute animation sur cet élément
      gsap.killTweensOf(gradientRef.current);

      // 🔧 CISCO: ANIMATION DOUCE avec nettoyage préalable
      gsap.to(gradientRef.current, {
        backgroundImage: gradient,
        duration: 20.0, // 🔧 CISCO: Transition plus douce et progressive
        ease: "power1.inOut", // 🔧 CISCO: Easing plus doux pour continuité
        force3D: true,
        onStart: () => {
          console.log(`🎨 MIDI: Démarrage transition vers - ${gradient}`);
        },
        onComplete: () => {
          console.log(`🎨 MIDI: Transition terminée - Dégradé bleu appliqué`);
        }
      });
    }

    if (landscapeRef.current) {
      gsap.to(landscapeRef.current, {
        filter: `brightness(${brightness})`,
        duration: 15.0,
        ease: "power2.inOut"
      });
    }
  };

  // 🎬 CISCO: applyAfternoonMode SUPPRIMÉ - Simplifié vers 4 modes cinématographiques

  const applySunsetMode = () => {
    console.log('🌇 APPLICATION MODE COUCHER - Continuité douce depuis midi - Réalisme pastel');
    const colors = BACKGROUND_MODES.sunset;
    const direction = getGradientDirection('sunset');
    // 🎨 CISCO: Continuité depuis midi - Bleu ciel en haut, orange pastel au milieu, rouge doux en bas
    const gradient = `linear-gradient(${direction}, ${colors.tertiary} 0%, ${colors.secondary} 60%, ${colors.primary} 100%)`;
    const brightness = 0.85; // 🔧 CISCO: Luminosité douce pour coucher réaliste

    if (gradientRef.current) {
      // 🔧 CISCO: NETTOYAGE EXPLICITE - Arrêter toute animation sur cet élément
      gsap.killTweensOf(gradientRef.current);

      // 🔧 CISCO: ANIMATION DOUCE avec nettoyage préalable
      gsap.to(gradientRef.current, {
        backgroundImage: gradient,
        duration: 18.0, // 🔧 CISCO: Transition plus douce pour continuité
        ease: "power1.inOut", // 🔧 CISCO: Easing plus doux
        force3D: true,
        onStart: () => {
          console.log(`🎨 COUCHER: Démarrage transition vers - ${gradient}`);
        },
        onComplete: () => {
          console.log(`🎨 COUCHER: Transition terminée - Dégradé coucher appliqué`);
        }
      });
    }

    if (landscapeRef.current) {
      gsap.to(landscapeRef.current, {
        filter: `brightness(${brightness})`,
        duration: 18.0, // 🔧 CISCO: Synchronisé avec le dégradé
        ease: "power1.inOut" // 🔧 CISCO: Easing plus doux
      });
    }
  };

  // 🎬 CISCO: applyDuskMode SUPPRIMÉ - Simplifié vers 4 modes cinématographiques

  // 🔧 CISCO: Changement de mode avec CROSS FADE progressif TOUJOURS
  const setBackgroundMode = (mode: BackgroundMode) => {
    // 🚀 CISCO: OPTIMISATION - Éviter les transitions inutiles
    if (currentModeRef.current === mode && !isTransitioning) {
      console.log(`⚡ Mode ${mode} déjà actif, transition ignorée`);
      return;
    }

    // 🔧 CISCO: PROTECTION RENFORCÉE - Arrêter TOUTES les animations en cours
    if (timelineRef.current && timelineRef.current.isActive()) {
      console.log('🛑 Interruption animation en cours pour nouvelle transition');
      timelineRef.current.kill();
      setIsTransitioning(false); // 🔧 CISCO: Forcer la réinitialisation du flag
    }

    // 🔧 CISCO: NETTOYAGE AGRESSIF - Tuer toutes les animations sur gradientRef
    if (gradientRef.current) {
      gsap.killTweensOf(gradientRef.current);
      console.log('🧹 Nettoyage agressif des animations GSAP sur gradientRef');
    }

    // 🔧 CISCO: PROTECTION ANTI-BLOCAGE - Forcer déblocage après 20s max
    if (isTransitioning) {
      console.log('⏳ Transition en cours, vérification anti-blocage...');
      setTimeout(() => {
        if (isTransitioning) {
          console.log('🔓 DÉBLOCAGE FORCÉ - Transition bloquée > 20s');
          setIsTransitioning(false);
          setBackgroundMode(mode); // Relancer la transition
        }
      }, 20000);
      return;
    }

    // Si c'est le même mode, ne rien faire (évite le spam de logs)
    if (mode === currentModeRef.current) {
      console.log('🔄 Mode identique, pas de transition');
      return;
    }

    console.log(`🎨 Changement de mode vers: ${mode} depuis ${currentModeRef.current}`);

    // 🔧 CISCO: TRANSITION DIRECTE SIMPLIFIÉE - Utiliser les fonctions spécialisées
    // Chaque mode utilise ses vraies couleurs sans transitions intermédiaires
    console.log(`🎯 TRANSITION DIRECTE vers ${mode} - Utilisation fonction spécialisée`);

    // 🌅 CISCO: Appliquer la fonction spécialisée selon le mode avec directions correctes
    switch (mode) {
      // 🎬 CISCO: 4 MODES CINÉMATOGRAPHIQUES SIMPLIFIÉS
      case 'dawn':
        applyDawnMode();
        break;
      case 'midday':
        applyMiddayMode();
        break;
      case 'sunset':
        applySunsetMode();
        break;
      case 'night':
        applyNightMode();
        break;

      default:
        // 🔧 CISCO: MAPPING DES ANCIENS MODES vers les nouveaux modes cinématographiques
        console.warn(`⚠️ Mode non reconnu: ${mode}, mapping vers mode cinématographique`);

        // Mapper les anciens modes vers les 4 modes cinématographiques
        let mappedMode: BackgroundMode;
        switch (mode) {
          case 'sunrise':
          case 'aube':
            mappedMode = 'dawn';
            break;
          case 'morning':
          case 'afternoon':
          case 'midi':
            mappedMode = 'midday';
            break;
          case 'dusk':
          case 'coucher':
            mappedMode = 'sunset';
            break;
          case 'nuit':
            mappedMode = 'night';
            break;
          default:
            console.warn(`🔧 Mode complètement inconnu: ${mode}, utilisation mode par défaut 'dawn'`);
            mappedMode = 'dawn';
            break;
        }

        console.log(`🔄 Mapping ${mode} → ${mappedMode}`);
        setBackgroundMode(mappedMode);
        break;
    }

    // 🔧 CISCO: Mettre à jour currentModeRef APRÈS l'application du mode
    currentModeRef.current = mode;
  };

  // 🔧 CISCO: FONCTION SUPPRIMÉE - updateBackgroundSmoothly obsolète
  // Remplacée par le système de mapping des modes vers les fonctions spécialisées

  // 🔧 CISCO: FONCTION SUPPRIMÉE - updateBackgroundWithBridge obsolète
  // Utilisation des fonctions spécialisées directement (applyDawnMode, applyMiddayMode, etc.)

  // 🔧 FONCTION SIMPLIFIÉE: Obtenir les couleurs pour un mode donné
  const getColorsForMode = (mode: BackgroundMode) => {
    // 🔧 CISCO: Protection renforcée contre les valeurs undefined/null
    if (!mode || mode === undefined || mode === null) {
      console.warn(`🔧 Mode invalide: ${mode}, utilisation du mode par défaut 'dawn'`);
      return BACKGROUND_MODES['dawn'];
    }

    const colors = BACKGROUND_MODES[mode];
    // 🔧 CISCO: Protection contre les modes non définis dans BACKGROUND_MODES
    if (!colors) {
      console.warn(`🔧 Mode non trouvé dans BACKGROUND_MODES: ${mode}, utilisation du mode par défaut 'dawn'`);
      return BACKGROUND_MODES['dawn'];
    }
    return colors;
  };

  // 🎬 CISCO: FONCTION CINÉMATOGRAPHIQUE SIMPLIFIÉE - 4 modes d'éclairage
  const getBrightnessForMode = (mode: BackgroundMode): number => {
    switch (mode) {
      case 'dawn': return 0.4;    // Aube : lumière douce progressive
      case 'midday': return 1.0;  // Midi : lumière au maximum
      case 'sunset': return 0.6;  // Coucher : lumière s'atténue
      case 'night': return 0.15;  // Nuit : lumière au minimum
      default: return 0.6;
    }
  };


  // 🔧 FONCTION PRINCIPALE: Transition progressive fluide entre modes
  const updateDynamicBackground = (mode?: BackgroundMode) => {
    // 🔧 CISCO: PROTECTION RENFORCÉE - Arrêter animations actives avant nouvelle transition
    if (timelineRef.current && timelineRef.current.isActive()) {
      console.log('🛑 Interruption animation active dans updateDynamicBackground');
      timelineRef.current.kill();
    }

    if (isTransitioning) { console.log('⏳ Transition déjà en cours, updateDynamicBackground ignoré'); return; }
    if (!gradientRef.current) return;

    const targetMode = mode || skyMode as BackgroundMode;
    const colors = getColorsForMode(targetMode);
    
    // 🎬 INDICATEUR DE TRANSITION
    setIsTransitioning(true);

    // 🎨 CISCO: Dégradé fluide avec direction correcte selon logique solaire
    const direction = getGradientDirection(targetMode);
    let gradient;
    if (direction === 'to top') {
      // PHASES MONTANTES - Couleurs dans l'ordre normal
      gradient = `linear-gradient(${direction}, ${colors.primary} 0%, ${colors.secondary} 60%, ${colors.tertiary} 100%)`;
    } else {
      // PHASES DESCENDANTES - Couleurs inversées pour effet descendant
      gradient = `linear-gradient(${direction}, ${colors.tertiary} 0%, ${colors.secondary} 60%, ${colors.primary} 100%)`;
    }
    
    const brightness = getBrightnessForMode(targetMode);

    console.log(`🎨 Transition progressive fluide vers ${targetMode}`);

    if (timelineRef.current) {
      timelineRef.current.kill();
    }

    timelineRef.current = gsap.timeline({
      onComplete: () => {
        setIsTransitioning(false);
        currentModeRef.current = targetMode; // ✅ Mettre à jour le mode courant pour éviter toute re-boucle
        console.log(`✨ Transition vers ${targetMode} terminée !`);
      }
    });

    // 🌅 CISCO: TRANSITION DIRECTE - SYNCHRONISÉE AVEC NUAGES
    timelineRef.current.to(gradientRef.current, {
      backgroundImage: gradient,
      duration: 15.0, // 🔧 CISCO: Synchronisé avec DiurnalLayer (15s)
      ease: "power1.inOut", // 🔧 CISCO: Easing synchronisé avec nuages
      force3D: true,
      willChange: "background-image"
    });

    // 🎬 CISCO: TRANSITION DE L'ÉCLAIRAGE CINÉMATOGRAPHIQUE - SYNCHRONISÉE
    const transitionDuration = 15.0; // 🔧 CISCO: Synchronisé avec DiurnalLayer
    if (landscapeRef.current) {
      timelineRef.current.to(landscapeRef.current, {
        filter: `brightness(${brightness})`,
        duration: transitionDuration, // 🔧 CISCO: Synchronisé avec DiurnalLayer (15s)
        ease: "power1.inOut" // 🔧 CISCO: Easing synchronisé avec nuages
      }, 0);
    }

  };

  // Animation de zoom du paysage
  const createLandscapeZoomAnimation = () => {
    if (!landscapeRef.current) return;
    if (zoomTimelineRef.current) {
      zoomTimelineRef.current.kill();
    }
    zoomTimelineRef.current = gsap.timeline({ repeat: -1, yoyo: false, force3D: true });
    zoomTimelineRef.current.to(landscapeRef.current, { scale: 1.15, duration: 45, ease: "power2.inOut" });
    zoomTimelineRef.current.to(landscapeRef.current, { scale: 1.15, duration: 5, ease: "none" });
    zoomTimelineRef.current.to(landscapeRef.current, { scale: 1.0, duration: 35, ease: "power2.out" });
    zoomTimelineRef.current.to(landscapeRef.current, { scale: 1.0, duration: 10, ease: "none" });
  };

  // 🔧 CISCO: Fonctions soleil supprimées - gérées dans AstronomicalLayer

  // 🌌 CISCO: Fonction SIMPLIFIÉE pour nuit profonde - Dégradé seulement
  const triggerNightAnimation = () => {
    console.log('🌌 DÉCLENCHEMENT NUIT PROFONDE - Dégradé seulement');
    updateDynamicBackground('night');
  };

  // Exposer les fonctions globalement
  (window as any).triggerNightAnimation = triggerNightAnimation; // CISCO: Animation nuit profonde

  // Initialisation une seule fois avec vraies couleurs Cisco
  useEffect(() => {
    // 🔧 CISCO: SUPPRESSION du dégradé initial - Évite les conflits avec les transitions
    // Le dégradé sera appliqué par setBackgroundMode() via updateDynamicBackground()
    console.log('🎨 Initialisation sans dégradé initial - Évite les conflits GSAP');

    createLandscapeZoomAnimation();

    // 🔧 CISCO: NETTOYAGE PRÉVENTIF - S'assurer qu'il n'y a pas de backgroundImage résiduel
    if (gradientRef.current) {
      gsap.set(gradientRef.current, {
        backgroundImage: 'none', // Nettoyer explicitement
        clearProps: "backgroundImage" // Supprimer toute propriété GSAP résiduelle
      });
    }

    updateDynamicBackground();

    // 🔧 CISCO: Initialiser l'éclairage du paysage pour le mode par défaut (dawn)
    if (landscapeRef.current) {
      const initialBrightness = getBrightnessForMode('dawn');
      gsap.set(landscapeRef.current, {
        filter: `brightness(${initialBrightness})`
      });
      console.log(`💡 Éclairage paysage initialisé pour dawn: brightness(${initialBrightness}) - Vraies couleurs Cisco`);
    }

    return () => {
      if (timelineRef.current) timelineRef.current.kill();
      if (zoomTimelineRef.current) zoomTimelineRef.current.kill();
    };
  }, []);

  // 🎬 CISCO: SYNCHRONISATION CINÉMATOGRAPHIQUE SIMPLIFIÉE - Props directes uniquement
  useEffect(() => {
    if (skyMode && skyMode !== (currentModeRef.current as string)) {
      // Mode cinématographique via props
      console.log(`🎬 Mode cinématographique: ${skyMode}`);
      setBackgroundMode(skyMode as BackgroundMode);
    } else if (!skyMode) {
      // Mode par défaut si pas de props
      console.log('🌅 Initialisation mode par défaut: dawn (aube)');
      setBackgroundMode(defaultMode as BackgroundMode);
    }
  }, [validatedSkyMode, skyMode]);



  // 🔧 CISCO: Exposer la fonction de changement de mode pour le contrôleur
  useEffect(() => {
    (window as any).setBackgroundMode = (mode: string) => {
      console.log(`🎨 Changement de mode via contrôleur: ${mode}`);
      setBackgroundMode(mode as BackgroundMode);
    };

    return () => {
      delete (window as any).setBackgroundMode;
    };
  }, []);

  return (
    <div
      ref={backgroundRef}
      className="relative overflow-hidden"
      style={{ minHeight: '100vh' }}
    >
      {/* Conteneur pour le dégradé - commence plus haut pour l'aube */}
      <div
        ref={gradientRef}
        className="absolute inset-0"
        style={{
          zIndex: 0,
          backgroundAttachment: 'fixed',
          backgroundRepeat: 'no-repeat',
          backgroundSize: 'cover'
          // ✅ CORRECTION: Supprimer le fallbackGradient qui entre en conflit avec GSAP
        }}
      />

      {/* Couches avec nuages réduits - Mode synchronisé avec le contexte */}
      <AstronomicalLayer skyMode={validatedSkyMode as BackgroundMode} />
      <DiurnalLayer skyMode={validatedSkyMode as BackgroundMode} />

      {/* 🔧 CISCO: SunriseAnimation déplacé dans AstronomicalLayer */}

      {/* Paysage avec éclairage dynamique - Background aléatoire */}
      <div
        ref={landscapeRef}
        className="fixed inset-0 w-full h-full bg-cover bg-center bg-no-repeat pointer-events-none"
        style={{
          backgroundImage: `url(${selectedBackground})`,
          backgroundPosition: getBackgroundPosition(), // Position pour Background.png
          backgroundSize: 'cover', // Taille standard pour tous les backgrounds
          zIndex: 10, // 🔧 CISCO: Paysage en avant-plan (z-index 10)
          transformOrigin: 'center center',
          willChange: 'transform, filter'
        }}
      />

      {/* Contenu principal */}
      <div className="relative" style={{ zIndex: 15 }}>
        {children}
      </div>

      {/* 🔧 CISCO: Indicateur de transition SUPPRIMÉ - Plus besoin d'affichage visuel */}
      {/* L'état isTransitioning reste pour la logique interne mais plus d'affichage */}

      <style dangerouslySetInnerHTML={{
        __html: `
          body, html {
            background: none !important;
            background-color: transparent !important;
          }
        `
      }} />
    </div>
  );
};

export default DynamicBackground;
